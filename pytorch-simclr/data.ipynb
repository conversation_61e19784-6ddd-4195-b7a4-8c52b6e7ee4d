# !pip install matplotlib

import torch, subprocess, os
print("CUDA available:", torch.cuda.is_available())
torch.zeros(1).cuda()
print("Tensor allocation on GPU OK")

!export CUDA_HOME=/usr/local/cuda
!export PATH=$CUDA_HOME/bin:$PATH
!export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH
!export CUDA_LAUNCH_BLOCKING=1

import torch
import torch.nn as nn
from torchvision.datasets import ImageFolder

class ImageDrugDataset(ImageFolder):
    def __init__(self, root, transform=None, target_transform=None, name="IDrugDataset"):
        super(ImageDrugDataset, self).__init__(root, transform, target_transform)
        self.name =  name

    def __getitem__(self, index):
        path, target = self.samples[index]
        sample = self.loader(path)
        if self.transform is not None:
            sample = self.transform(sample)
        if self.target_transform is not None:
            target = self.target_transform(target)
        return sample, target, index
    def __repr__(self):
        return f"{self.name}({len(self.samples)} images from {len(self.classes)} classes)"



MOBILE_PATH ="/root/source/std-dataset/aryashah2k/mobile-captured-pharmaceutical-medication-packages/versions/1/Mobile-Captured Pharmaceutical Medication Packages"
mobile_ds = ImageDrugDataset(root=MOBILE_PATH, transform=None, target_transform=None, name="mobile_ds")

REVIEW_VALID_PATH = "/root/source/thuocsi_mixed_dataset"
thuocsi_mixed_dataset = ImageDrugDataset(
    root=REVIEW_VALID_PATH,
    transform=None,
    target_transform=None,
    name="thuocsi_mixed_dataset",
)
len(thuocsi_mixed_dataset.classes)

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
device

from tqdm import tqdm
import torchvision.transforms as transforms
from PIL import Image


def pil_collate_fn(batch):
    """
    Custom collate function that converts PIL images to tensors
    """
    images = []
    targets = []
    indices = []

    for image, target, idx in batch:
        # Convert PIL Image to tensor
        if isinstance(image, Image.Image):
            image = transforms.ToTensor()(image)
        images.append(image)
        targets.append(target)
        indices.append(idx)

    # Stack tensors
    images = torch.stack(images)
    targets = torch.tensor(targets)
    indices = torch.tensor(indices)

    return images, targets, indices


def compute_mean_std_extremely_memory_efficient(dataset):
    """Compute mean and std with minimal memory usage by processing images one by one"""
    # Initialize variables for online calculation
    n_samples = 0
    channel_sum = torch.zeros(3)
    channel_sum_squared = torch.zeros(3)

    # Process one image at a time
    to_tensor = transforms.ToTensor()

    for idx in tqdm(
        range(len(dataset)), desc=f"Computing mean and std of {dataset.name}"
    ):
        # Get a single image
        img, _, _ = dataset[idx]

        # Convert to tensor if it's a PIL image
        if isinstance(img, Image.Image):
            img = to_tensor(img)

        # Add batch dimension for consistent processing
        if img.dim() == 3:
            img = img.unsqueeze(0)  # [C,H,W] -> [1,C,H,W]

        # Update running statistics
        b, c, h, w = img.shape
        num_pixels = b * h * w

        # Sum up values for each channel
        channel_sum += torch.sum(img, dim=[0, 2, 3])
        channel_sum_squared += torch.sum(img**2, dim=[0, 2, 3])

        n_samples += num_pixels

        # Free memory
        del img

        # Optional: force garbage collection periodically
        if idx % 1000 == 0:
            import gc

            gc.collect()

    # Calculate mean and std
    mean = channel_sum / n_samples
    std = torch.sqrt((channel_sum_squared / n_samples) - (mean**2))

    return mean.tolist(), std.tolist()


CACHED_MEAN_STD = {
    "mobile": (
        [0.5171197652816772, 0.477710098028183, 0.48365601897239685],
        [0.25186651945114136, 0.2183791846036911, 0.2162138819694519],
    ),
    "review_valid": (
        [0.7648352384567261, 0.7523052096366882, 0.7378935813903809],
        [0.3109317421913147, 0.30783846974372864, 0.3260281980037689],
    ),
    "thuocsi_mixed_dataset": (
        [0.6589288115501404, 0.7182037234306335, 0.6760091781616211],
        [0.3528593182563782, 0.2799980044364929, 0.32041865587234497],
    ),
}

CACHED_MEAN_STD

CACHED_MEAN_STD_CP = {
    "mobile": (
        [0.5171197652816772, 0.477710098028183, 0.48365601897239685],
        [0.25186651945114136, 0.2183791846036911, 0.2162138819694519],
    ),
    "review_valid": (
        [0.7648352384567261, 0.7523052096366882, 0.7378935813903809],
        [0.3109317421913147, 0.30783846974372864, 0.3260281980037689],
    ),
    "thuocsi_mixed_dataset": (
        [0.6589288115501404, 0.7182037234306335, 0.6760091781616211],
        [0.3528593182563782, 0.2799980044364929, 0.32041865587234497],
    ),
}

# CACHED_MEAN_STD_COPY = {
#     "mobile": (
#         [0.5171197652816772, 0.477710098028183, 0.48365601897239685],
#         [0.25186651945114136, 0.2183791846036911, 0.2162138819694519],
#     ),
#     "review_valid": (
#         [0.7648352384567261, 0.7523052096366882, 0.7378935813903809],
#         [0.3109317421913147, 0.30783846974372864, 0.3260281980037689],
#     ),
# }

import matplotlib.pyplot as plt
import numpy as np

def visualize_normalization(dataset, mean, std, num_samples=5):
    """
    Visualize the distribution of pixel values before and after normalization
    
    Args:
        dataset: The image dataset
        mean: List of mean values for each channel [r_mean, g_mean, b_mean]
        std: List of std values for each channel [r_std, g_std, b_std]
        num_samples: Number of random images to sample
    """
    # Convert mean and std to tensors
    mean_tensor = torch.tensor(mean).view(3, 1, 1)
    std_tensor = torch.tensor(std).view(3, 1, 1)
    
    # Create normalization transform
    normalize = transforms.Normalize(mean=mean, std=std)
    to_tensor = transforms.ToTensor()
    
    # Sample random images
    indices = np.random.choice(len(dataset), num_samples, replace=False)
    
    # Create figure
    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 4*num_samples))
    
    channel_names = ['Red', 'Green', 'Blue']
    
    for i, idx in enumerate(indices):
        # Get image
        img, _, _ = dataset[idx]
        
        # Convert to tensor if it's a PIL image
        if isinstance(img, Image.Image):
            img_tensor = to_tensor(img)
        else:
            img_tensor = img
            
        # Normalize image
        normalized_img = normalize(img_tensor)
        
        # Display original image
        if i == 0:
            axes[i, 0].set_title("Original Image")
        axes[i, 0].imshow(img_tensor.permute(1, 2, 0).clamp(0, 1))
        axes[i, 0].axis('off')
        
        # Plot histograms for each channel
        for c in range(3):
            # Original distribution
            channel_data = img_tensor[c].flatten().numpy()
            axes[i, 1].hist(channel_data, bins=50, alpha=0.5, 
                          label=channel_names[c], color=f'C{c}')
            
            # Normalized distribution
            norm_channel_data = normalized_img[c].flatten().numpy()
            axes[i, 2].hist(norm_channel_data, bins=50, alpha=0.5,
                          label=channel_names[c], color=f'C{c}')
            
        if i == 0:
            axes[i, 1].set_title("Original Distribution")
            axes[i, 1].legend()
            axes[i, 2].set_title("Normalized Distribution")
            axes[i, 2].legend()
            
        # Add mean and std info
        if i == num_samples - 1:
            mean_text = f"Mean: [{mean[0]:.3f}, {mean[1]:.3f}, {mean[2]:.3f}]"
            std_text = f"Std: [{std[0]:.3f}, {std[1]:.3f}, {std[2]:.3f}]"
            fig.text(0.5, 0.01, mean_text + "\n" + std_text, ha='center', fontsize=12)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.08)
    plt.show()
    
    # Also plot the expected normal distribution after normalization
    plt.figure(figsize=(10, 6))
    x = np.linspace(-3, 3, 1000)
    plt.plot(x, 1/(np.sqrt(2*np.pi)) * np.exp(-x**2/2), 'k-', lw=2, 
             label='Standard Normal Distribution')
    plt.title("Expected Distribution After Normalization")
    plt.xlabel("Pixel Value")
    plt.ylabel("Density")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

# Example usage:
mean, std = CACHED_MEAN_STD["mobile"]
visualize_normalization(mobile_ds, mean, std, num_samples=3)

def verify_normalization_stats(dataset, mean, std, num_samples=100):
    """
    Verify that normalization actually produces data with mean ≈ 0 and std ≈ 1
    """
    # Create normalization transform
    normalize = transforms.Normalize(mean=mean, std=std)
    to_tensor = transforms.ToTensor()
    
    # Sample random images
    indices = np.random.choice(len(dataset), min(num_samples, len(dataset)), replace=False)
    
    # Initialize accumulators
    normalized_sum = torch.zeros(3)
    normalized_sum_squared = torch.zeros(3)
    pixel_count = 0
    
    for idx in tqdm(indices, desc="Verifying normalization"):
        # Get image
        img, _, _ = dataset[idx]
        
        # Convert to tensor if it's a PIL image
        if isinstance(img, Image.Image):
            img_tensor = to_tensor(img)
        else:
            img_tensor = img
            
        # Normalize image
        normalized_img = normalize(img_tensor)
        
        # Update statistics
        c, h, w = normalized_img.shape
        pixel_count += h * w
        
        normalized_sum += torch.sum(normalized_img, dim=[1, 2])
        normalized_sum_squared += torch.sum(normalized_img ** 2, dim=[1, 2])
    
    # Calculate mean and std of normalized data
    normalized_mean = normalized_sum / pixel_count
    normalized_std = torch.sqrt((normalized_sum_squared / pixel_count) - (normalized_mean ** 2))
    
    print(f"Normalized data statistics:")
    print(f"Mean: {normalized_mean.tolist()} (should be close to [0, 0, 0])")
    print(f"Std: {normalized_std.tolist()} (should be close to [1, 1, 1])")
    
    # Check if values are close to expected
    mean_ok = all(abs(m) < 0.01 for m in normalized_mean.tolist())
    std_ok = all(abs(s - 1.0) < 0.01 for s in normalized_std.tolist())
    
    if mean_ok and std_ok:
        print("✅ Normalization verified successfully!")
    else:
        print("⚠️ Normalization may not be optimal. Check the values above.")
        
# Example usage:
mean, std = CACHED_MEAN_STD["thuocsi_mixed_dataset"]
verify_normalization_stats(thuocsi_mixed_dataset, mean, std)

def check_class_balance(dataset):
    """
    Check the class distribution in the dataset
    """
    # Count instances per class
    class_counts = {}
    for _, target, _ in tqdm(dataset, desc="Counting classes"):
        if target in class_counts:
            class_counts[target] += 1
        else:
            class_counts[target] = 1
    
    # Convert to sorted list of (class_idx, count) pairs
    class_counts = sorted(class_counts.items())
    
    # Get class names if available
    class_names = getattr(dataset, 'classes', None)
    
    # Plot class distribution
    plt.figure(figsize=(12, 6))
    counts = [count for _, count in class_counts]
    x = range(len(counts))
    
    plt.bar(x, counts)
    plt.xlabel('Class Index')
    plt.ylabel('Number of Samples')
    plt.title('Class Distribution in Dataset')
    
    # Add class names as x-tick labels if available
    if class_names:
        if len(class_names) > 30:
            # If too many classes, show only some labels
            step = max(1, len(class_names) // 30)
            plt.xticks(
                x[::step], 
                [class_names[i] for i in range(0, len(class_names), step)],
                rotation=90
            )
        else:
            plt.xticks(x, class_names, rotation=90)
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    total = sum(counts)
    min_count = min(counts)
    max_count = max(counts)
    imbalance_ratio = max_count / min_count if min_count > 0 else float('inf')
    
    print(f"Total samples: {total}")
    print(f"Number of classes: {len(counts)}")
    print(f"Samples per class: min={min_count}, max={max_count}, avg={total/len(counts):.1f}")
    print(f"Imbalance ratio (max/min): {imbalance_ratio:.2f}")
    
# Example usage:
check_class_balance(thuocsi_mixed_dataset)

def visualize_dataset_samples(dataset, rows=5, cols=5):
    """
    Visualize random samples from the dataset
    """
    # Get random indices
    indices = np.random.choice(
        len(dataset), min(rows * cols, len(dataset)), replace=False
    )

    # Create figure
    fig, axes = plt.subplots(rows, cols, figsize=(cols * 3, rows * 3))
    axes = axes.flatten()

    # Get class names if available
    class_names = getattr(dataset, "classes", None)

    for i, idx in tqdm(enumerate(indices)):
        if i >= len(axes):
            break

        # Get image and label
        img, label, _ = dataset[idx]

        # Convert to tensor if it's a PIL image
        if isinstance(img, Image.Image):
            img_tensor = transforms.ToTensor()(img)
        else:
            img_tensor = img

        # Display image
        axes[i].imshow(img_tensor.permute(1, 2, 0).clamp(0, 1))

        # Add label as title
        title = f"Class: {label}"
        if class_names and label < len(class_names):
            title = f"Class: {class_names[label]}"
        axes[i].set_title(title)
        axes[i].axis("off")

    plt.tight_layout()
    plt.show()


# Example usage:
visualize_dataset_samples(thuocsi_mixed_dataset, rows=10, cols=10)

def check_for_corrupted_images(dataset):
    """
    Check for corrupted images in the dataset
    """
    corrupted = []
    
    for idx in tqdm(range(len(dataset)), desc="Checking for corrupted images"):
        try:
            # Try to load the image
            img, _, _ = dataset[idx]
            
            # Check if image is valid
            if isinstance(img, Image.Image):
                # For PIL images, check size
                if img.size[0] == 0 or img.size[1] == 0:
                    corrupted.append(idx)
            else:
                # For tensors, check for NaNs or very extreme values
                if torch.isnan(img).any() or torch.isinf(img).any():
                    corrupted.append(idx)
        except Exception as e:
            print(f"Error loading image at index {idx}: {str(e)}")
            corrupted.append(idx)
    
    if corrupted:
        print(f"Found {len(corrupted)} corrupted images at indices: {corrupted[:10]}...")
        if len(corrupted) > 10:
            print(f"(showing only first 10 of {len(corrupted)})")
    else:
        print("✅ No corrupted images found!")


# Example usage:
check_for_corrupted_images(thuocsi_mixed_dataset)


def verify_transforms(dataset, transforms_list, num_samples=3):
    """
    Visualize the effect of different transforms on sample images
    """
    # Get random indices
    indices = np.random.choice(len(dataset), min(num_samples, len(dataset)), replace=False)
    
    # Number of transforms to visualize
    num_transforms = len(transforms_list)
    
    # Create figure
    fig, axes = plt.subplots(num_samples, num_transforms + 1, figsize=(3 * (num_transforms + 1), 3 * num_samples))
    
    for i, idx in enumerate(indices):
        # Get original image
        img, _, _ = dataset[idx]
        
        # Display original image
        if isinstance(img, Image.Image):
            # Keep a copy of the PIL image
            original_img = img.copy()
            # Convert to tensor for display
            img_tensor = transforms.ToTensor()(img)
            axes[i, 0].imshow(img_tensor.permute(1, 2, 0).clamp(0, 1))
        else:
            # If already a tensor, convert to PIL for transforms
            original_img = transforms.ToPILImage()(img)
            axes[i, 0].imshow(img.permute(1, 2, 0).clamp(0, 1))
            
        axes[i, 0].set_title("Original")
        axes[i, 0].axis('off')
        
        # Apply each transform and display
        for t_idx, transform in enumerate(transforms_list):
            # Apply transform
            transformed_img = transform(original_img)
            
            # Convert to tensor if it's a PIL image
            if isinstance(transformed_img, Image.Image):
                transformed_tensor = transforms.ToTensor()(transformed_img)
            else:
                transformed_tensor = transformed_img
                
            # Display transformed image
            axes[i, t_idx + 1].imshow(transformed_tensor.permute(1, 2, 0).clamp(0, 1))
            axes[i, t_idx + 1].set_title(transform.__class__.__name__)
            axes[i, t_idx + 1].axis('off')
    
    plt.tight_layout()
    plt.show()


from PIL import ImageFilter, Image
from torchvision import transforms


def ColourDistortion(s=1.0):
    # s is the strength of color distortion.
    color_jitter = transforms.ColorJitter(0.8 * s, 0.8 * s, 0.8 * s, 0.2 * s)
    rnd_color_jitter = transforms.RandomApply([color_jitter], p=0.8)
    rnd_gray = transforms.RandomGrayscale(p=0.2)
    color_distort = transforms.Compose([rnd_color_jitter, rnd_gray])
    return color_distort


def BlurOrSharpen(radius=2.0):
    blur = GaussianBlur(radius=radius)
    full_transform = transforms.RandomApply([blur], p=0.5)
    return full_transform


class ImageFilterTransform(object):

    def __init__(self):
        raise NotImplementedError

    def __call__(self, img):
        return img.filter(self.filter)


class GaussianBlur(ImageFilterTransform):

    def __init__(self, radius=2.0):
        self.filter = ImageFilter.GaussianBlur(radius=radius)


class Sharpen(ImageFilterTransform):

    def __init__(self):
        self.filter = ImageFilter.SHARPEN


mean, std = CACHED_MEAN_STD["thuocsi_mixed_dataset"]

# check_for_corrupted_images(mobile_ds)
# Verify transforms
# transforms_to_check = [
#     transforms.RandomHorizontalFlip(),
#     transforms.ColorJitter(brightness=0.2),
#     transforms.RandomRotation(15),
#     transforms.Compose(
#         [transforms.ToTensor(), transforms.Normalize(mean=mean, std=std)]
#     ),
# ]


transform_train = [
    transforms.Compose(
        [
            transforms.RandomResizedCrop(
                (224, 224), scale=(0.2, 1), interpolation=Image.BICUBIC
            ),
            transforms.RandomHorizontalFlip(),
            transforms.RandomRotation(15),   
            ColourDistortion(s=0.5),
            transforms.ToTensor(),
            transforms.Normalize(mean=mean, std=std),
        ]
    )
]


transform_test = [
    transforms.Compose(
        [
            transforms.Resize(256, interpolation=Image.BICUBIC),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=mean, std=std),
        ]
    )
]

verify_transforms(thuocsi_mixed_dataset, transform_train, num_samples=10)

import time
import torch
import psutil  # pip install psutil
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
import os

torch.cuda.empty_cache()

os.environ["CUDA_LAUNCH_BLOCKING"] = "1"


def benchmark_dataloader(
    dataset: torch.utils.data.Dataset,
    batch_size: int = 32,
    num_workers: int = 4,
    num_batches: int = 50,
    pin_memory: bool = True,
):
    """
    Measure how many samples/sec you can push through this DataLoader,
    and report average CPU utilization over that period.
    """

    def pil_collate_fn(batch):
        """
        Custom collate function that converts PIL images to tensors
        """
        images = []
        targets = []
        indices = []

        for image, target, idx in batch:
            # Convert PIL Image to tensor
            if isinstance(image, Image.Image):
                image = transforms.ToTensor()(image)
            images.append(image)
            targets.append(target)
            indices.append(idx)

        # Stack tensors
        images = torch.stack(images)
        targets = torch.tensor(targets)
        indices = torch.tensor(indices)

        return images, targets, indices

    loader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        collate_fn=pil_collate_fn,
        # pin_memory_device="cuda:0",
    )
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print("device:", device)
    # warm-up one pass
    for _ in loader:
        break

    # start CPU monitoring
    psutil.cpu_percent(interval=None)  # clear old data
    t0 = time.time()
    for i, batch in enumerate(loader):
        # print("batch:", len(batch), type(batch))
        if i + 1 >= num_batches:
            break
        # if you want to separate input/target timing:
        inputs, targets = batch[0], batch[1]
        _ = inputs.to(device, non_blocking=True)
        _ = targets.to(device, non_blocking=True)  # simulate copy
    elapsed = time.time() - t0
    cpu_used = psutil.cpu_percent(interval=None)

    samples = batch_size * num_batches
    print(
        f"""
    ———————————————————————————————
    Benchmark results
    — batches:        {num_batches}
    — batch_size:     {batch_size}
    — workers:        {num_workers}
    — pin_memory:     {pin_memory}
    — elapsed (s):    {elapsed:.2f}
    — throughput:     {samples/elapsed:.1f} samples/sec
    — CPU util avg.:  {cpu_used:.1f} %
    ———————————————————————————————
    """
    )


thuocsi_mixed_dataset.transform = transform_train[0]
for nw in [0, 2, 4, 8, 12, 16, 20]:
    print(f"\n==> Testing num_workers={nw}")
    benchmark_dataloader(
        thuocsi_mixed_dataset,
        batch_size=128,
        num_workers=nw,
        num_batches=20,
        pin_memory=True,
    )

# from torchvision  import transforms


# class StemImageNet(nn.Module):
#     def __init__(self):
#         super(StemImageNet, self).__init__()
#         self.inplanes = 64
#         self.conv1 = nn.Conv2d(3, self.inplanes, kernel_size=7, stride=2, padding=3, bias=False)
#         self.bn1 = nn.BatchNorm2d(self.inplanes)
#         self.relu = nn.ReLU(inplace=True)
#         self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)

#     def forward(self, x):
#         x = self.conv1(x)
#         x = self.bn1(x)
#         x = self.relu(x)
#         x = self.maxpool(x)
#         return x
    
    
# transform  = transforms.Compose(
#         [
#             transforms.RandomResizedCrop(
#                 (224, 224), scale=(0.2, 1), interpolation=Image.BICUBIC
#             ),
#             transforms.RandomHorizontalFlip(),
#             transforms.RandomRotation(15),   
#             ColourDistortion(s=0.5),
#             transforms.ToTensor(),
#             transforms.Normalize(mean=mean, std=std),
#         ]
#     )